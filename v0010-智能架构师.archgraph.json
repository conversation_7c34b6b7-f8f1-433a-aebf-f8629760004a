{"arch_info": {"project_name": "v0.010智能架构师提示词系统设计", "arch_version": "v1.0", "created_date": "2025-01-28", "last_updated": "2025-01-28 10:30"}, "core_architecture": {"modules": ["需求理解引擎（问题分析和需求澄清）", "信息收集器（多源信息获取和整合）", "双外脑管理器（CogniGraph + ArchGraph协调）", "方案设计器（技术架构和实现方案）", "任务规划器（任务分解和优先级管理）", "代码实现器（分步执行和实时测试）", "质量验证器（功能测试和文档同步）"], "dependencies": ["需求理解引擎 → 信息收集器（需求驱动信息搜索）", "信息收集器 → 方案设计器（信息支撑方案设计）", "方案设计器 → 双外脑管理器（方案存储和管理）", "方案设计器 → 任务规划器（方案驱动任务分解）", "任务规划器 → 代码实现器（任务指导代码实现）", "代码实现器 → 质量验证器（实现结果验证）", "双外脑管理器 → 所有模块（状态同步和数据共享）"], "interfaces": ["架构师决策接口（支持复杂架构决策和方案对比）", "三外脑数据接口（CogniGraph、ArchGraph、ArchWisdom数据访问）", "五步审讯法接口（画地图、讲人话、找茬、给剧本、扮演魔鬼）", "架构模式查询接口（模式匹配和推荐服务）", "多视图设计接口（4视图架构设计和管理）", "流程执行接口（14阶段精准执行控制）", "质量评估接口（多维质量检查和验证）"], "data_flow": ["用户需求 → 需求理解引擎 → 需求分析 → 信息收集器", "收集信息 → 方案设计器 → 技术方案 → ArchGraph存储", "技术方案 → 任务规划器 → 任务列表 → CogniGraph存储", "任务执行 → 代码实现器 → 实现结果 → 质量验证器", "验证结果 → 双外脑管理器 → 状态更新 → 项目完成"]}, "tech_stack": {"languages": ["Python 3.9+（主要开发语言，统一脚本编写）"], "frameworks": ["JSON Schema（数据结构验证和规范）", "Mermaid（架构图可视化生成）", "Sequential Thinking（复杂决策分析工具）"], "databases": ["JSON文件存储（轻量级数据持久化）", "架构模式库（结构化模式和最佳实践存储）"], "tools": ["Tavily（网络搜索和信息收集）", "Context7（技术文档和API参考）", "GitHub工具集（代码管理和协作）", "Playwright（自动化测试和验证）"]}, "deployment": {"environment": "AI助手集成环境（支持多工具调用和文件操作）", "structure": "模块化架构（松耦合、高内聚的组件设计）", "requirements": ["支持JSON文件读写和结构化数据处理", "支持多工具协同调用和状态管理", "支持复杂逻辑推理和决策分析", "支持架构图生成和可视化输出"]}}